"use client";
import { WarningIcon2 } from "@/shared/icons/Icon";
import React from "react";

interface SubscriptionCancelPopupProps {
  isVisible: boolean;
}

const SubscriptionCancelPopup: React.FC<SubscriptionCancelPopupProps> = ({
  isVisible,
}) => {
  if (!isVisible) return null;

  return (
    <div
      className={`fixed max-w-md bottom-2 right-2 bg-white border-t border-gray-200 shadow-lg transform transition-transform duration-300 ease-in-out z-50 ${
        isVisible ? "translate-y-0" : "translate-y-full"
      }`}
    >
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="bg-red-50 p-2 rounded-full">
              <WarningIcon2 />
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 font-inter">
              Subscription Cancelled
            </h3>
            <p className="text-sm text-gray-600 font-inter">
              Your subscription has been cancelled. You&apos;ve been moved to the
              Basic plan.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionCancelPopup;
