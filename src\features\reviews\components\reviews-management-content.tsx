'use client';

import { useEffect, useState, useCallback } from 'react';
import { TReview } from '../types/review.type';
import { getReviewsForManagement } from '../actions/reviews.action';
import { useReviewsHandlers } from '../lib/use-reviews-handlers';
import ReviewsStatsGrid from './reviews-stats-grid';
import ReviewsFilters from './reviews-filters';
import ReviewsCardsList from './reviews-cards-list';

const ReviewsManagementContent = () => {
  const [reviews, setReviews] = useState<TReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentFilter, setCurrentFilter] = useState('all');
  const [currentSearch, setCurrentSearch] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({
    total: 0,
    totalPages: 0,
    currentPage: 1,
    perPage: 10,
    hasNext: false,
    hasPrev: false
  });

  const fetchReviews = useCallback(async (filter = currentFilter, search = currentSearch, page = currentPage) => {
    try {
      setLoading(true);
      setError(null);

      const response = await getReviewsForManagement({
        filter: filter !== 'all' ? filter : undefined,
        search: search.trim() || undefined,
        page: page > 1 ? page : undefined,
        limit: 10
      });

      if (response.success && response.data) {
        setReviews(response.data.reviews || []);
        setPagination(response.data.pagination || pagination);
      } else {
        setError(response.message || 'Failed to fetch reviews');
        setReviews([]);
      }
    } catch (error) {
      console.error('Failed to fetch reviews:', error);
      setError('An unexpected error occurred while fetching reviews');
      setReviews([]);
    } finally {
      setLoading(false);
    }
  }, [currentFilter, currentSearch, currentPage, pagination]);

  useEffect(() => {
    fetchReviews();
  }, [fetchReviews]);

  // Handle filter changes
  const handleFilterChange = (newFilter: string) => {
    setCurrentFilter(newFilter);
    setCurrentPage(1);
    fetchReviews(newFilter, currentSearch, 1);
  };

  // Handle search changes with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (currentSearch !== '') {
        setCurrentPage(1);
        fetchReviews(currentFilter, currentSearch, 1);
      } else if (currentSearch === '') {
        // Trigger search when search is cleared
        setCurrentPage(1);
        fetchReviews(currentFilter, '', 1);
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [currentSearch, currentFilter, fetchReviews]);

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    fetchReviews(currentFilter, currentSearch, newPage);
  };

  const {
    selectedReview,
    setSelectedReview,
    moderationResponse,
    setModerationResponse,
    stats,
    handleAddNote,
    handleDeleteReview,
    handleStatusChange,
    handleFlag,
    handleHide,
    handleRestore,
    handlePublish,
    handleModerationResponse,
    handleDropdownViewDetails,
    handleDropdownAddReview
  } = useReviewsHandlers(reviews);

  const handleReplyUpdate = (reviewId: string, replyText: string) => {
    // Update the local state with the new reply
    setReviews(prevReviews => 
      prevReviews.map(review => 
        review.id === reviewId 
          ? { 
              ...review, 
              replyText, 
              replyDate: new Date().toISOString(),
              repliedBy: 'current-user' // You might want to get this from session/context
            }
          : review
      )
    );
  };

  // Create enhanced handlers that refresh data after operations
  const enhancedHandlers = {
    handleStatusChange: async (id: string, status: TReview['status']) => {
      await handleStatusChange(id, status);
      // Refresh the current view to reflect server state
      fetchReviews(currentFilter, currentSearch, currentPage);
    },
    handleFlag: async (id: string, reason: string) => {
      await handleFlag(id, reason);
      fetchReviews(currentFilter, currentSearch, currentPage);
    },
    handleHide: async (id: string, reason: string) => {
      await handleHide(id, reason);
      fetchReviews(currentFilter, currentSearch, currentPage);
    },
    handleRestore: async (id: string) => {
      await handleRestore(id);
      fetchReviews(currentFilter, currentSearch, currentPage);
    },
    handlePublish: async (id: string) => {
      await handlePublish(id);
      fetchReviews(currentFilter, currentSearch, currentPage);
    },
    handleDeleteReview: async (id: string) => {
      await handleDeleteReview(id);
      fetchReviews(currentFilter, currentSearch, currentPage);
    },
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center space-y-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <p className="text-gray-600">Loading reviews...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h3 className="text-lg font-medium text-red-800 mb-2">Error Loading Reviews</h3>
            <p className="text-red-600">{error}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="mt-3 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
  <div className="space-y-6">
    <ReviewsStatsGrid />

    <div className="bg-white rounded-lg shadow">
      {/* Filters Section */}
      <div className="p-2 border-b">
        <ReviewsFilters 
          searchTerm={currentSearch}
          onSearchChange={setCurrentSearch}
          selectedFilter={currentFilter}
          onFilterChange={handleFilterChange}
        />
      </div>

      {/* Reviews List Section */}
      <div className="p-4">
        {loading ? (
          <div className="flex justify-center items-center h-32">
            <div className="flex flex-col items-center space-y-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <p className="text-gray-600 text-sm">
                {currentFilter !== 'all' || currentSearch ? 'Applying filters...' : 'Loading reviews...'}
              </p>
            </div>
          </div>
        ) : reviews.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-500">
              {currentFilter !== 'all' || currentSearch ? (
                <div>
                  <p className="text-lg font-medium mb-2">No reviews found</p>
                  <p className="text-sm">
                    Try adjusting your filters or search terms to see more results.
                  </p>
                  <button
                    onClick={() => {
                      setCurrentFilter('all');
                      setCurrentSearch('');
                      fetchReviews('all', '', 1);
                    }}
                    className="mt-3 px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Clear all filters
                  </button>
                </div>
              ) : (
                <div>
                  <p className="text-lg font-medium mb-2">No reviews yet</p>
                  <p className="text-sm">Reviews will appear here once they are submitted.</p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <ReviewsCardsList
            reviews={reviews}
            filteredReviews={reviews}
            onStatusChange={enhancedHandlers.handleStatusChange}
            onFlag={enhancedHandlers.handleFlag}
            onRestore={enhancedHandlers.handleRestore}
            onHide={enhancedHandlers.handleHide}
            onPublish={enhancedHandlers.handlePublish}
            onViewDetails={handleDropdownViewDetails}
            onAddReview={handleDropdownAddReview}
            onAddNote={handleAddNote}
            onReply={(id) => console.log('Reply to review:', id)}
            onReplyUpdate={handleReplyUpdate}
          />
        )}
        
        {/* Pagination Controls */}
        {pagination.total > 0 && pagination.totalPages > 1 && (
          <div className="mt-6 flex justify-center items-center space-x-2">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={!pagination.hasPrev}
              className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                const pageNum = Math.max(1, Math.min(
                  pagination.totalPages - 4,
                  Math.max(1, currentPage - 2)
                )) + i;
                
                if (pageNum <= pagination.totalPages) {
                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`px-3 py-2 text-sm font-medium rounded-md ${
                        pageNum === currentPage
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                }
                return null;
              })}
            </div>
            
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={!pagination.hasNext}
              className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        )}
        
        {/* Pagination Info */}
        {pagination.total > 0 && (
          <div className="mt-6 text-center text-sm text-gray-600">
            Showing {reviews.length} of {pagination.total} reviews
            {pagination.totalPages > 1 && (
              <span className="ml-2">
                (Page {pagination.currentPage} of {pagination.totalPages})
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  </div>
);

};

export default ReviewsManagementContent;
